{
    "compilerOptions": {
        "moduleResolution": "Node",
        "baseUrl": "./",
        "paths": {
            "@/*": ["src/*"],
            "react": ["./node_modules/@types/react"]
        },
        "types": ["@baidu/bce-react-toolkit/es/types/global.d.ts", "./src/index.d.ts"],
        // cba打包使用babel-loader 所以target、lib、module配置无效
        "target": "es2017",
        "lib": ["DOM", "ES2015"],
        "module": "AMD",
        "jsx": "react",
        "outDir": "dist",
        "strict": false,
        "allowJs": true,
        "esModuleInterop": true,
        "allowSyntheticDefaultImports": true,
        "allowUmdGlobalAccess": true,
        "noImplicitAny": false,
        "experimentalDecorators": true
    },
    "include": ["src"],
    "exclude": ["node_modules", "dist"]
}
