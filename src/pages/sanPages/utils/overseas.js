/**
 * @file 海外登录相关工具函数
 * <AUTHOR>

import axios from 'axios';

const AllRegion = window.$context.getEnum('AllRegion');

/**
 * 检查是否为海外账号登录
 * @returns {boolean} 是否为海外账号登录
 */
export function isOverseasLogin() {
    if (window.$framework && window.$framework.overseas) {
        // const isOverseasLogin: boolean = overseas.isOverseasLogin();
        return window.$framework.overseas.isOverseasLogin();
    }
    return false;
}

/**
 * 检查海外信息是否完善
 * 直接调用 api/iam/account/area/info 接口
 * 调用时请求头的x-region要固定为bj
 * @returns {Promise<boolean>} 海外信息是否完善
 */
export async function isOverseasInfoCompleted() {
    console.log('🔍 [Overseas Debug] 开始直接调用 api/iam/account/area/info 接口');

    try {
        // 准备请求头，确保 x-region 固定为 bj
        const headers = {
            'x-region': 'bj',
            'X-Region': 'bj',
            'Content-Type': 'application/json'
        };

        console.log('🔍 [Overseas Debug] 请求头设置:', headers);
        console.log('🔍 [Overseas Debug] 请求URL: /api/iam/account/area/info');

        // 直接调用接口
        const response = await axios({
            method: 'GET',
            url: '/api/iam/account/area/info',
            headers: headers,
            timeout: 10000 // 10秒超时
        });

        console.log('✅ [Overseas Debug] 接口调用成功');
        console.log('✅ [Overseas Debug] 响应状态:', response.status);
        console.log('✅ [Overseas Debug] 响应头:', response.headers);
        console.log('✅ [Overseas Debug] 响应数据:', response.data);

        // 根据接口返回的数据判断海外信息是否完善
        // 这里需要根据实际的接口返回格式来判断
        const isCompleted = response.data?.completed || response.data?.isCompleted || false;

        console.log('✅ [Overseas Debug] 海外信息完善状态:', isCompleted);
        return isCompleted;

    } catch (error) {
        console.error('❌ [Overseas Debug] api/iam/account/area/info 接口调用失败:', error);
        console.error('❌ [Overseas Debug] 错误类型:', error.constructor.name);
        console.error('❌ [Overseas Debug] 错误消息:', error.message);

        if (error.response) {
            console.error('❌ [Overseas Debug] 响应状态:', error.response.status);
            console.error('❌ [Overseas Debug] 响应状态文本:', error.response.statusText);
            console.error('❌ [Overseas Debug] 响应头:', error.response.headers);
            console.error('❌ [Overseas Debug] 响应数据:', error.response.data);
        } else if (error.request) {
            console.error('❌ [Overseas Debug] 请求配置:', error.config);
            console.error('❌ [Overseas Debug] 请求对象:', error.request);
        }

        // 出错时默认为未完善，保证安全性
        return false;
    }
}

/**
 * 检查是否需要实名认证
 * 海外账号登录且信息完善时不需要实名认证
 * @returns {Promise<boolean>} 是否需要实名认证
 */
export async function shouldVerifyUser() {
    // 如果是海外账号登录
    if (isOverseasLogin()) {
        // 检查海外信息是否完善
        const isCompleted = await isOverseasInfoCompleted();
        if (isCompleted) {
            return false; // 海外账号且信息完善，不需要实名认证
        }
    }

    // 非海外账号或海外账号信息未完善，需要实名认证
    return true;
}