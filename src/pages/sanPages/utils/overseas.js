/**
 * @file 海外登录相关工具函数
 * <AUTHOR>

const AllRegion = window.$context.getEnum('AllRegion');

/**
 * 检查是否为海外账号登录
 * @returns {boolean} 是否为海外账号登录
 */
export function isOverseasLogin() {
    if (window.$framework && window.$framework.overseas) {
        // const isOverseasLogin: boolean = overseas.isOverseasLogin();
        return window.$framework.overseas.isOverseasLogin();
    }
    return false;
}

/**
 * 检查海外信息是否完善
 * 只有当前地域是北京region时才调用这个方法
 * 调用时请求头的x-region要固定为bj
 * @returns {Promise<boolean>} 海外信息是否完善
 */
export async function isOverseasInfoCompleted() {
    // const isOverseasLogin: boolean = overseas.isOverseasLogin();
    // if (!isOverseasLogin()) {
    //     return true;
    // }

    // 只有当前地域是北京或香港region时才调用这个方法
    // const currentRegion = ContextService.getCurrentRegionId();
    // if (currentRegion !== AllRegion.BJ && currentRegion !== AllRegion.HK02) {
    //     return true; // 非北京/香港region直接返回true
    // }

    if (window.$framework && window.$framework.overseas && window.$framework.overseas.isOverseasInfoCompleted) {
        try {
            // 确保请求头x-region固定为bj
                        const result = await window.$framework.overseas.isOverseasInfoCompleted
               
            return result;

        } catch (error) {
            console.error('检查海外信息完善状态失败:', error);
            // 出错时默认为未完善，保证安全性
            return false;
        }
    }


    return true; // 如果没有相关方法，默认为已完善
}

/**
 * 检查是否需要实名认证
 * 海外账号登录且信息完善时不需要实名认证
 * @returns {Promise<boolean>} 是否需要实名认证
 */
export async function shouldVerifyUser() {
    // 如果是海外账号登录
    if (isOverseasLogin()) {
        // 检查海外信息是否完善
        const isCompleted = await isOverseasInfoCompleted();
        if (isCompleted) {
            return false; // 海外账号且信息完善，不需要实名认证
        }
    }

    // 非海外账号或海外账号信息未完善，需要实名认证
    return true;
}